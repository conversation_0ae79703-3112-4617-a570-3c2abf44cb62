import dgram from 'dgram'
import { BrowserWindow } from 'electron'

// 创建 UDP 服务器
const server = dgram.createSocket('udp4')
const client = dgram.createSocket('udp4')

// 配置端口
const LISTEN_PORT = 50020
const BROADCAST_PORT = 50021
const BROADCAST_ADDR = import.meta.env.VITE_APP_UDPADDR // 广播地址

// 初始化 UDP 服务
function initUdpServices(): void {
  console.log('广播地址：', BROADCAST_ADDR)
  // 设置服务器错误处理
  server.on('error', (err) => {
    console.error(`UDP 服务器错误: ${err.message}`)
    server.close()
  })

  // 处理接收到的消息
  server.on('message', (msg, rinfo) => {
    const content = msg.toString()
    const timestamp = new Date().toISOString()
    console.log(`接收到来自 ${rinfo.address}:${rinfo.port} 的消息: ${content}`)

    // 向渲染进程发送消息
    const mainWindow = BrowserWindow.getAllWindows()[0]
    if (mainWindow) {
      mainWindow.webContents.send('udp-message', {
        content,
        timestamp,
        sender: `${rinfo.address}:${rinfo.port}`
      })
    }
  })

  // 服务器开始监听
  server.on('listening', () => {
    const address = server.address()
    console.log(`UDP 服务器监听 ${address.address}:${address.port}`)
  })

  // 绑定端口
  server.bind(LISTEN_PORT)

  // 设置客户端错误处理
  client.on('error', (err) => {
    console.error(`UDP 客户端错误: ${err.message}`)
    client.close()
  })

  // 允许广播
//   client.setBroadcast(true)
}

// 发送 UDP 消息
function sendUdpMessage(message: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const buffer = Buffer.from(message)

    client.send(buffer, 0, buffer.length, BROADCAST_PORT, BROADCAST_ADDR, (err) => {
      if (err) {
        console.error(`发送消息失败: ${err.message}`)
        reject(`发送失败: ${err.message}`)
      } else {
        console.log(`消息已发送: ${message}`)
        resolve('消息发送成功')
      }
    })
  })
}

// 发送 UDP 消息
function logCaptureImage(message: string): Promise<string> {
  return new Promise(() => {
    console.log(`渲染线程截图消息: ${message}`)
  })
}

export default {
  initUdpServices,
  sendUdpMessage,
  logCaptureImage
}
