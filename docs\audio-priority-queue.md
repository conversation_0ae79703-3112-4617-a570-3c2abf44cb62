# 音频优先级队列系统

## 概述

本系统实现了一个带优先级的音频播放队列，确保音频按照优先级顺序播放，同时避免频繁打断当前播放的音频。

## 核心特性

1. **不立即打断播放**: 当前播放的音频会完整播放完毕，不会被新音频立即打断
2. **单一队列槽位**: 播放队列中只保存一个等待播放的音频
3. **优先级替换**: 如果新音频的优先级高于队列中的音频，则替换；否则抛弃新音频

## 优先级设置

音频类型按优先级从低到高排列：

```javascript
audioPriorityMap = {
  'hello': 1,      // shooting状态的音频 (最低优先级)
  'catch': 2,      // catch状态的音频
  'create': 3,     // getFace状态的音频  
  'createOver': 4  // createOver状态的音频 (最高优先级)
}
```

## 工作流程

### 1. 音频入队逻辑

```
新音频请求 → 检查当前播放状态
                ↓
        是否正在播放音频？
                ↓
        是 → 检查队列中是否有等待音频
                ↓
        有 → 比较优先级
                ↓
        新音频优先级更高？
                ↓
        是 → 替换队列中的音频
        否 → 抛弃新音频
```

### 2. 音频播放流程

```
当前音频播放完毕 → 检查队列
                    ↓
            队列中有等待音频？
                    ↓
            有 → 延迟2-8秒后播放
            无 → 等待新音频
```

## API 接口

### 核心方法

- `queueAudio(audioType, immediate)`: 将音频添加到队列
- `processAudioQueue()`: 处理队列中的音频
- `getAudioPriority(audioType)`: 获取音频优先级
- `clearAudioQueue()`: 清空队列
- `getQueueStatus()`: 获取队列状态

### 测试方法

- `testPriorityQueue()`: 自动测试优先级队列功能
- `showQueueStatus()`: 显示当前队列状态

## 使用示例

### 基本使用

```javascript
// 添加音频到队列（会根据优先级处理）
_CatStatus.queueAudio('hello')
_CatStatus.queueAudio('createOver') // 高优先级，会替换hello

// 立即播放（清空队列）
_CatStatus.queueAudio('catch', true)
```

### 状态变化触发

```javascript
// 通过状态变化自动触发音频播放
_CatStatus.changeStatus('shooting')  // 触发hello音频
_CatStatus.changeStatus('catch')     // 触发catch音频
_CatStatus.changeStatus('getFace')   // 触发create音频
_CatStatus.changeStatus('createOver') // 触发createOver音频
```

## 测试场景

### 优先级替换测试

1. 播放低优先级音频 (hello)
2. 添加高优先级音频 (createOver) → 应该替换队列中的hello
3. 添加低优先级音频 (catch) → 应该被抛弃
4. 添加更高优先级音频 (create) → 应该替换队列中的createOver

### 队列状态监控

使用 `getQueueStatus()` 可以获取：
- 是否正在播放音频
- 队列中等待的音频类型和优先级
- 是否有延迟播放定时器

## 日志输出

系统会输出详细的日志信息，包括：
- 音频入队决策过程
- 优先级比较结果
- 队列状态变化
- 播放开始和结束事件

## 注意事项

1. 队列中只保存一个音频，新音频会根据优先级决定是否替换
2. 当前播放的音频不会被打断，只有在播放完成后才会处理队列
3. 优先级相同的音频，后来的会替换先来的
4. immediate参数可以强制立即播放并清空队列
