import { createRouter, createWebHashHistory } from "vue-router";

const routes = [
  { path: "/", component: () =>import("./components/cat.vue") },
  // { path: "/clickbutton", component: import("./components/avaturn/upload2") },
  {
    path: "/test",
    component: () => import("./components/test.vue"),
  },
  { path: "/testOnnx", component: () =>import("./components/testOnnx.vue") }
];
// console.log(VueRouter);
export default createRouter({
  // 4. Provide the history implementation to use. We are using the hash history for simplicity here.
  history: createWebHashHistory(),
  routes, // short for `routes: routes`
});
