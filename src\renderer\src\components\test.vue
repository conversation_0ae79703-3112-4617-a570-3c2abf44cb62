<template>
    <div>
        <div>Test</div>
        
    </div>
</template>

<script setup>
import * as ort from 'onnxruntime-web';
import { onMounted } from 'vue';
let env = ort.env
console.log(env )
// ort.env.wasm.wasmPaths = '/onnx/'
onMounted(async () => {
    try {
        // 创建推理会话
        const session = await ort.InferenceSession.create('flow.decoder.estimator.fp32.onnx');
        
        // 创建输入 tensor
        // 注意：这里的输入格式需要根据您的模型要求来设置
        const inputTensor = new ort.Tensor('string', ['你好'], [1]);
        
        // 运行模型
        const result = await session.run({
            // 这里的 input_name 需要替换为您模型的实际输入名称
            input_name: inputTensor
        });
        
        console.log('推理结果:', result);
    } catch (error) {
        console.error('模型运行错误:', error);
    }
});




</script>

<style scoped>

</style>

