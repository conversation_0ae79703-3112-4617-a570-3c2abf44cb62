<template>
    <div>
        <div>Test</div>
        <div v-for="item in vars.morphs" :key="item">
            {{ item }}
            <div style="width: 500px;height: 10px;background-color: gainsboro;">
                <div style="height: 10px;background-color: red;" :style="{width: item * 500 + 'px'}"></div>
            </div>
        </div>
        <audio src="/audio.mp3" controls></audio>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import Lipsync from '../utils/threelipsync-master/threelipsync-master/threelipsync.js'

const lipsync = new Lipsync();

let vars = ref({
    morphs:[]
});

setTimeout(() => {
    // lipsync.startMic()
    lipsync.init()
    lipsync.startSample('/zhenpiaoliang.mp3')
    // lipsync.playSample()
    setInterval(() => {
        let arr = lipsync.update();
        lipsync.context.resume()
        if (arr){
// console.log(lipsync.update());
            arr.map((item,index) => {
                        vars.value.morphs[index] = item
                    })
        }
        
    }, 50);
    
}, 1000);






</script>

<style scoped>

</style>

